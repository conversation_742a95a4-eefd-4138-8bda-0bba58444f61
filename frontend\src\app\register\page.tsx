'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import GoogleLoginButton from '@/components/auth/GoogleLoginButton';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Divider,
} from '@mui/material';

export default function Register() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { register } = useAuth();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // For simplified form, we'll use email prefix as first name and "User" as last name
      const emailPrefix = formData.email.split('@')[0];
      const registrationData = {
        firstName: emailPrefix || 'User',
        lastName: 'User',
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.password,
      };

      const result = await register(registrationData);

      if (result.succeeded) {
        router.push('/dashboard');
      } else {
        setError(result.errors?.join(', ') || 'Registration failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
      console.error('Registration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: 'background.default',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Container component="main" maxWidth="xs">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            px: 4,
          }}
        >
          {/* Cherish Logo */}
          <Typography
            variant="h3"
            component="h1"
            sx={{
              mb: 4,
              fontWeight: 300,
              color: 'text.primary',
              '& .accent': {
                color: 'warning.main',
              },
            }}
          >
            cher<span className="accent">i</span>sh
          </Typography>

          {/* Sign up heading */}
          <Typography
            variant="h4"
            component="h2"
            sx={{
              mb: 1,
              fontWeight: 400,
              color: 'warning.main',
            }}
          >
            Sign up
          </Typography>

          {/* Subtitle */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 3, textAlign: 'center' }}
          >
            Sign up using your account
          </Typography>

          {/* Google Login Button */}
          <Box sx={{ width: '100%', mb: 3 }}>
            <GoogleLoginButton
              text="Continue with Google"
              onError={(errorMessage) => setError(errorMessage)}
            />
          </Box>

          {/* Divider */}
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 3 }}>
            <Divider sx={{ flex: 1, borderColor: 'text.secondary' }} />
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mx: 2, fontSize: '0.875rem' }}
            >
              Or sign up with your email
            </Typography>
            <Divider sx={{ flex: 1, borderColor: 'text.secondary' }} />
          </Box>

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Form */}
          <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
            <TextField
              required
              fullWidth
              id="email"
              placeholder="Your email address"
              name="email"
              autoComplete="email"
              autoFocus
              value={formData.email}
              onChange={handleChange}
              sx={{ mb: 2 }}
              InputProps={{
                sx: {
                  borderRadius: 1,
                  '& input::placeholder': {
                    color: '#9ca3af',
                    opacity: 1,
                  },
                },
              }}
            />

            <TextField
              required
              fullWidth
              name="password"
              placeholder="Password"
              type="password"
              id="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              sx={{ mb: 3 }}
              InputProps={{
                sx: {
                  borderRadius: 1,
                  '& input::placeholder': {
                    color: '#9ca3af',
                    opacity: 1,
                  },
                },
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{
                py: 1.5,
                backgroundColor: 'warning.main',
                color: 'warning.contrastText',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: '#f59e0b',
                },
                '&:disabled': {
                  backgroundColor: '#d1d5db',
                  color: '#6b7280',
                },
                mb: 3,
              }}
            >
              {isLoading ? 'Signing up...' : 'Sign up'}
            </Button>

            {/* Sign in link */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Already a member?{' '}
                <Link
                  href="/login"
                  style={{
                    color: '#fbbf24',
                    textDecoration: 'underline',
                    fontWeight: 500,
                  }}
                >
                  Sign in
                </Link>
              </Typography>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
}
