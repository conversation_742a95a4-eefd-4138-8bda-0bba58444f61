'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  CssBaseline,
  Menu,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  Security as SecurityIcon,
  MonitorHeart as MonitorHeartIcon,
  Payments as PaymentsIcon,
  Tune as TuneIcon,
  AccountCircle,
  Notifications as NotificationsIcon,
  VpnKey as VpnKeyIcon,
} from '@mui/icons-material';

const drawerWidth = 240;

export default function SuperAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/dashboard');
      }
    } else if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isLoading, isAuthenticated, user, router]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return null;
  }

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleProfileMenuClose();
    logout();
  };

  const menuItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/super-admin/dashboard' },
    { text: 'Tenants', icon: <BusinessIcon />, path: '/super-admin/tenants' },
    { text: 'Users', icon: <PeopleIcon />, path: '/super-admin/users' },
    { text: 'Roles', icon: <VpnKeyIcon />, path: '/super-admin/roles' },
    { text: 'Permissions', icon: <SecurityIcon />, path: '/super-admin/permissions' },
    { text: 'System Config', icon: <TuneIcon />, path: '/super-admin/config' },
    { text: 'System Health', icon: <MonitorHeartIcon />, path: '/super-admin/health' },
    { text: 'Audit Logs', icon: <SecurityIcon />, path: '/super-admin/audit-logs' },
    { text: 'Billing', icon: <PaymentsIcon />, path: '/super-admin/billing' },
  ];

  const drawer = (
    <Box sx={{ bgcolor: '#2d3748', height: '100%', color: 'white' }}>
      <Toolbar sx={{ bgcolor: '#2d3748', borderBottom: '1px solid #4a5568' }}>
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            color: 'white',
            fontWeight: 'bold',
            '& .accent': {
              color: '#fbbf24',
            },
          }}
        >
          cher<span className="accent">i</span>sh
        </Typography>
      </Toolbar>
      <Box sx={{ p: 2 }}>
        <Box
          sx={{
            bgcolor: '#fbbf24',
            color: '#000',
            borderRadius: 1,
            px: 2,
            py: 1.5,
            mb: 2,
            textAlign: 'center',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '&:hover': {
              bgcolor: '#f59e0b',
            },
          }}
          component={Link}
          href={user?.roles.includes('SuperAdmin') ? '/super-admin/dashboard' : '/dashboard'}
        >
          <Typography variant="body2" fontWeight="bold" sx={{ fontSize: '0.875rem' }}>
            Go to Home
          </Typography>
        </Box>
      </Box>
      <Typography
        variant="overline"
        sx={{
          color: '#a0aec0',
          px: 2,
          fontSize: '0.75rem',
          fontWeight: 'bold',
          letterSpacing: '0.1em',
          mb: 1,
          display: 'block'
        }}
      >
        {user?.roles.join(', ') || 'Admin'}
      </Typography>
      <List sx={{ px: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              component={Link}
              href={item.path}
              sx={{
                borderRadius: 1,
                mx: 1,
                mb: 0.5,
                color: 'white',
                '&:hover': {
                  bgcolor: '#4a5568',
                },
                '&.Mui-selected': {
                  bgcolor: '#4a5568',
                },
              }}
            >
              <ListItemIcon sx={{ color: 'white', minWidth: 36 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: '#4a4a4a',
          color: 'white',
          borderBottom: '1px solid #6b7280',
        }}
        elevation={0}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, color: 'white' }}>
            Super Admin
          </Typography>
          <IconButton sx={{ color: 'white' }}>
            <NotificationsIcon />
          </IconButton>
          <IconButton
            onClick={handleProfileMenuOpen}
            sx={{ color: 'white' }}
          >
            <AccountCircle />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem component={Link} href="/dashboard/profile" onClick={handleProfileMenuClose}>
          Profile
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          Logout
        </MenuItem>
      </Menu>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              bgcolor: '#2d3748',
              borderRight: 'none',
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              bgcolor: '#2d3748',
              borderRight: 'none',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          bgcolor: '#4a4a4a',
          minHeight: '100vh',
          color: 'white',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
}
