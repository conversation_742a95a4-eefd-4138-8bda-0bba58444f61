{"name": "cherish-fullstack", "version": "1.0.0", "description": "Cherish - Employee Rewards & Recognition Platform", "private": true, "scripts": {"dev": "node scripts/start-sequential.js", "dev:concurrent": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:sequential": "node scripts/start-sequential.js", "dev:backend": "cd backend/Cherish.Api && dotnet run", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && dotnet build", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && dotnet restore", "install:all": "npm run install:frontend && npm run install:backend", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf .next node_modules", "clean:backend": "cd backend && dotnet clean", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && dotnet test", "lint:frontend": "cd frontend && npm run lint", "start:prod": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend/Cherish.Api && dotnet run --configuration Release", "start:frontend": "cd frontend && npm start"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["employee-recognition", "rewards", "dotnet", "nextjs", "typescript", "postgresql"], "author": "Cherish Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["frontend"]}